# 扣费系统重构说明

## 重构概述

本次重构在 `UserPointServiceImpl` 内部实现了简易版的责任链+策略模式，优化了原有的扣费计算逻辑，提升了代码的可维护性和可扩展性。

## 设计模式应用

### 🔗 责任链模式
将扣费计算过程分解为多个处理步骤：
- **特权检查** → **数量调整** → **积分扣除**

### 🎯 策略模式  
根据不同创作类型选择不同的处理链：
- **图片创作策略**: 模型积分 → 赠送积分 → 缪斯点
- **视频创作策略**: 时长调整 → 缪斯点
- **修复类策略**: 强制单张 → 缪斯点
- **换头换背景策略**: 赠送积分 → 缪斯点

## 文件结构

### 新增工具类
```
src/main/java/ai/conrain/aigc/platform/service/util/
├── FeeCalculationContext.java        # 扣费计算上下文
└── FeeCalculationComparator.java     # 新旧结果对比工具
```

### 修改文件
```
UserPointServiceImpl.java             # 主要重构文件
├── calcPredict()                     # 主入口，新旧对比
├── calcPredictNew()                  # 新逻辑（策略选择）
├── calcPredictOld()                  # 旧逻辑（保留对比）
└── 责任链处理器方法们                   # 各种扣费步骤
```

## 核心实现

### 1. 主入口方法
```java
private PredictVO calcPredict(CreativeTypeEnum type, Integer modelId, ...) {
    // 1. 构建上下文
    FeeCalculationContext context = new FeeCalculationContext(...);
    
    // 2. 执行新旧逻辑
    PredictVO newResult = calcPredictNew(context);
    PredictVO oldResult = calcPredictOld(context);
    
    // 3. 对比结果并返回旧结果（保证兼容性）
    return FeeCalculationComparator.compareAndLog(...);
}
```

### 2. 策略选择
```java
private PredictVO calcPredictNew(FeeCalculationContext context) {
    // 特权检查
    if (checkPrivilege(context)) return context.getResult();
    
    // 根据创作类型选择策略
    switch (context.getType()) {
        case CREATE_IMAGE: executeImageCreationChain(context); break;
        case CREATE_VIDEO: executeVideoCreationChain(context); break;
        case REPAIR_DETAIL: executeDirectMusePointChain(context); break;
        // ...
    }
    
    return context.getResult();
}
```

### 3. 责任链执行
```java
// 图片创作责任链
private void executeImageCreationChain(FeeCalculationContext context) {
    adjustImageCount(context);                    // 数量调整
    if (context.getRemainingCount() > 0) {
        deductModelPoint(context);                // 模型积分扣除
    }
    if (context.getRemainingCount() > 0) {
        deductGivePoint(context);                 // 赠送积分扣除
    }
    if (context.getRemainingCount() > 0) {
        deductMusePoint(context);                 // 缪斯点扣除
    }
}
```

## 扣费策略配置

| 创作类型 | 处理链路 | 说明 |
|---------|---------|------|
| CREATE_IMAGE | 数量调整 → 模型积分 → 赠送积分 → 缪斯点 | 图片创作完整链路 |
| FIXED_POSTURE_CREATION | 数量调整(双倍) → 模型积分 → 赠送积分 → 缪斯点 | 固定姿势创作 |
| CREATE_VIDEO | 数量调整(时长) → 缪斯点 | 视频创作 |
| REPAIR_DETAIL 等 | 数量调整(强制单张) → 缪斯点 | 修复类功能 |
| FACE_SCENE_SWITCH | 数量调整 → 赠送积分 → 缪斯点 | 换头换背景 |
| 其他类型 | 数量调整 → 缪斯点 | 通用策略 |

## 新旧代码对比

### 对比日志示例
```
[扣费对比] ✅ 结果一致 - type=CREATE_IMAGE, userId=1001, modelId=100, imageNum=2
[扣费对比] ❌ 结果不一致 - type=CREATE_VIDEO, userId=1002
  新结果: {"musePoint":5.0,"needTopup":false}
  旧结果: {"musePoint":4.8,"needTopup":false}
```

### 安全机制
1. **双重计算**: 新旧逻辑同时执行
2. **结果对比**: 详细对比各个字段
3. **兼容性保证**: 始终返回旧逻辑结果
4. **异常处理**: 分别捕获新旧逻辑异常

## 扩展新类型

添加新的创作类型 `AI_PAINTING`：

```java
// 在 calcPredictNew() 的 switch 中添加
case AI_PAINTING:
    executeAIPaintingChain(context);
    break;

// 实现对应的责任链
private void executeAIPaintingChain(FeeCalculationContext context) {
    adjustImageCount(context);
    if (context.getRemainingCount() > 0) {
        deductGivePoint(context);      // 只用赠送积分
    }
    if (context.getRemainingCount() > 0) {
        deductMusePoint(context);      // 然后缪斯点
    }
}
```

## 重构优势

### ✅ **代码结构清晰**
- 策略选择逻辑清晰
- 责任链步骤分明
- 每个方法职责单一

### ✅ **易于扩展**
- 新增创作类型: 添加新策略
- 新增扣费步骤: 添加新处理器
- 调整扣费顺序: 重新组合责任链

### ✅ **高度复用**
- 处理器可在不同策略中复用
- 减少代码重复

### ✅ **安全重构**
- 新旧代码并行运行
- 详细的对比日志
- 保证向后兼容

### ✅ **便于测试**
- 每个处理器可独立测试
- 策略选择逻辑可单独验证

## 后续计划

1. **观察对比日志**: 确认新旧逻辑一致性
2. **完善单元测试**: 覆盖各种扣费场景
3. **性能监控**: 确保重构不影响性能
4. **逐步切换**: 验证稳定后切换到新逻辑
5. **移除旧代码**: 最终移除旧的计算逻辑

通过这种渐进式的重构方式，既保证了系统的稳定性，又提升了代码的可维护性和可扩展性。
