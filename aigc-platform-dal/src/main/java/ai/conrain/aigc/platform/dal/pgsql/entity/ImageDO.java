package ai.conrain.aigc.platform.dal.pgsql.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 图像基本信息表
 * 对应数据表：image
 */
@Data
public class ImageDO implements Serializable {
    /**
     * 图像ID，自增主键
     */
    private Integer id;

    /**
     * 图像类型，用于区分服装图、风格示意图
     */
    private String type;

    /**
     * 图像原始URL地址
     */
    private String url;

    /**
     * 展示图像URL地址
     */
    private String showImgUrl;

    /**
     * 图片路径
     */
    private String imagePath;

    /**
     * 图片内容哈希
     */
    private String imageHash;

    /**
     * 图像元数据，JSON格式
     */
    private String metadata;

    /**
     * 拓展信息
     */
    private String extInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 软删除标记
     */
    private Boolean deleted;

    private static final long serialVersionUID = 1L;

    public void andLogicalDeleted(boolean deleted) {
        setDeleted(deleted ? Deleted.IS_DELETED.value() : Deleted.NOT_DELETED.value());
    }

    public enum Deleted {
        NOT_DELETED(new Boolean("false"), "未删除"),
        IS_DELETED(new Boolean("true"), "已删除");

        private final Boolean value;

        private final String name;

        Deleted(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        public Boolean getValue() {
            return this.value;
        }

        public Boolean value() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }

        public static Deleted parseValue(Boolean value) {
            if (value != null) {
                for (Deleted item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        public static Deleted parseName(String name) {
            if (name != null) {
                for (Deleted item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }
}