package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.integration.aliyun.OssService;
import ai.conrain.aigc.platform.service.component.creative.async.BatchToAsyncExecutor;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ModelTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.request.PartialRedrawRequest;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeElementVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeTaskVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Service
public class PartialRedrawCreativeService extends AbstractCreativeService<PartialRedrawRequest> {

    @Autowired
    private BatchToAsyncExecutor generalTransAsyncExecutor;
    @Autowired
    private OssService ossService;
    @Autowired
    private BatchFillHelper batchFillHelper;

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.PARTIAL_REDRAW;
    }

    @Override
    protected CreativeBatchVO buildData(PartialRedrawRequest request, MaterialModelVO modelVO) throws IOException {

        //1.合并原始图片和mask，设置alpha通道
        BufferedImage maskImage = ImageIO.read(request.getMask().getInputStream());

        //1.1.从oss中获取原始图片
        String originImage = request.getOriginImage();
        CreativeTaskVO task = null;
        String imageProportion = null;
        Integer taskId = request.getTaskId() != null ? request.getTaskId() : FileUtils.getTaskIdByImageName(
            request.getOriginImage());

        if (taskId != null) {
            task = creativeTaskService.selectById(taskId);
            AssertUtil.assertNotNull(task, ResultCode.PARAM_INVALID, "创作任务不存在");
            if (StringUtils.isBlank(originImage)) {
                originImage = task.getResultImages().get(0);
            }
            imageProportion = task.getImageProportion();
        }

        String imageUrl = CommonUtil.getFilePathAndNameFromURL(originImage);
        String originImageName = CommonUtil.getFileNameWithoutExtension(imageUrl);
        String tmpUrl = ossService.downloadFile(imageUrl, "/tmp/", originImageName);
        BufferedImage originalImage = ImageIO.read(new File(tmpUrl));

        //1.2.合并图片和蒙层
        BufferedImage mergedImage = FileUtils.combine4DMask(originalImage, maskImage);
        if (StringUtils.isBlank(imageProportion)) {
            imageProportion = mergedImage.getHeight() == mergedImage.getWidth() ? (mergedImage.getWidth() > 1024
                ? ProportionTypeEnum.ONE_ONE_LG.getCode() : ProportionTypeEnum.ONE_ONE.getCode())
                : ProportionTypeEnum.THREE_FOUR.getCode();
        }

        // 2. 上传合并后的图片
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(mergedImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        String imageName = originImageName + "_repair_hands.png";
        // 2.1 上传到 oss
        ByteArrayInputStream bais = new ByteArrayInputStream(imageBytes);
        String ossUrl = ossService.upload(imageName, bais);
        String md5 = CommonUtil.calculateMD5(imageBytes);
        // 2.2 上传到 comfyui的input下
        String inputImagePath = ComfyUIUtils.buildInputPath(OperationContextHolder.getMasterUserId()) + imageName;
        fileDispatch.uploadFile(inputImagePath, new ByteArrayInputStream(imageBytes), ossUrl,
            OperationContextHolder.getMasterUserId(), false, null, md5);

        ModelTypeEnum modelType = ModelTypeEnum.CUSTOM;
        Integer modelId = request.getModelId();
        if (task != null && task.getModelId() != null) {
            MaterialModelVO model = materialModelService.selectById(task.getModelId());
            if (!ObjectUtils.isEmpty(model)) {
                modelType = model.getType();
                modelId = modelId == null ? task.getModelId() : modelId;

                //系统模型，不传modelId和类型，防止访问/creation时无法现实数据
                if (modelType == ModelTypeEnum.SYSTEM) {
                    modelId = null;
                    modelType = null;
                }
            }
        }
        //3.生成创作批次记录
        CreativeBatchVO data = buildRedrawBatch(task, inputImagePath, ossUrl, modelType, originImage, imageProportion,
            modelId, request.getImageSource());

        //4.翻译提示词
        generalTransAsyncExecutor.storeSync(request, data);

        data.setBatchCnt(request.getImageNum());

        return data;
    }

    /**
     * 构建修复手势的批次信息
     *
     * @param task            任务
     * @param inputImagePath  input下文件地址
     * @param ossUrl          图片地址
     * @param modelType       模型类型
     * @param originImage     原始图片
     * @param imageProportion 图片比例尺寸
     * @param modelId         服装id
     * @return 批次信息
     */
    @NotNull
    private CreativeBatchVO buildRedrawBatch(CreativeTaskVO task, String inputImagePath, String ossUrl,
                                             ModelTypeEnum modelType, String originImage, String imageProportion,
                                             Integer modelId, String imageSource) {
        CreativeBatchVO data = new CreativeBatchVO();
        data.setStatus(CreativeStatusEnum.QUEUE);
        data.setShowImage(originImage);
        data.setImageProportion(imageProportion);
        data.addExtInfo(KEY_REDRAW_IMAGE, inputImagePath);
        data.addExtInfo(KEY_REDRAW_IMAGE_OSS, ossUrl);
        data.addExtInfo(KEY_ORIGIN_IMAGE, originImage);

        data.setUserId(OperationContextHolder.getMasterUserId());
        data.setOperatorId(OperationContextHolder.getOperatorUserId());
        data.setModelType(modelType);
        data.setType(CreativeTypeEnum.PARTIAL_REDRAW);
        if (StringUtils.equals(HISTORY, imageSource)) {
            data.addExtInfo(KEY_IS_CREATIVE_UPLOAD, NO);
        } else {
            data.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        }
        if (task != null) {
            data.addExtInfo(KEY_ORIGIN_TASK, task.getId());
            data.setModelId(task.getModelId());
        }
        if (modelId != null) {
            data.setModelId(modelId);
        }
        batchFillHelper.fillOriginBatchInfo(ossUrl, task == null ? null : task.getId(), data);
        return data;
    }

    @Override
    protected void fillTaskExt(CreativeTaskVO target, CreativeBatchVO batch, List<CreativeElementVO> elements,
                               int idx) {
        target.addExtInfo(KEY_REDRAW_IMAGE, batch.getStringFromExtInfo(KEY_REDRAW_IMAGE));
        generalTransAsyncExecutor.restoreTask(target, batch);
    }

    @Override
    protected void preParse(CreativeTaskVO task, List<CreativeElementVO> elements, MaterialModelVO modelVO,
                            Map<String, Object> context) {
        generalTransAsyncExecutor.asyncExecAndStore(task, elements);
        context.put(KEY_REDRAW_IMAGE, task.getStringFromExtInfo(KEY_REDRAW_IMAGE));
        context.put(KEY_TRANS_REDRAW_DESC, task.getStringFromExtInfo(KEY_TRANS_REDRAW_DESC));
        context.put("seed2", RandomStringUtils.randomNumeric(15));
    }

    @Override
    protected String postParse(String prompt) {
        prompt = prompt.replaceAll("\r\n|\r|\n", "");
        prompt = prompt.replaceAll("\\\\n", "\\\\n");
        return prompt;
    }
}
