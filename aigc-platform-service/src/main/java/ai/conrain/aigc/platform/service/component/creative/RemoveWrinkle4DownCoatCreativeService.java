package ai.conrain.aigc.platform.service.component.creative;

import ai.conrain.aigc.platform.service.component.creative.async.RemoveWrinkleAsyncExecutor;
import ai.conrain.aigc.platform.service.constants.CommonConstants;
import ai.conrain.aigc.platform.service.enums.CreativeStatusEnum;
import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.helper.BatchFillHelper;
import ai.conrain.aigc.platform.service.helper.RemoveWrinkleTaskHelper;
import ai.conrain.aigc.platform.service.model.request.RemoveWrinkle4DownCoatRequest;
import ai.conrain.aigc.platform.service.model.vo.CommonTaskVO;
import ai.conrain.aigc.platform.service.model.vo.CreativeBatchVO;
import ai.conrain.aigc.platform.service.model.vo.MaterialModelVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.DateUtils;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import com.alibaba.excel.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static ai.conrain.aigc.platform.service.constants.CommonConstants.*;

@Slf4j
@Service
public class RemoveWrinkle4DownCoatCreativeService extends AbstractCreativeService<RemoveWrinkle4DownCoatRequest> {

    @Autowired
    private BatchFillHelper batchFillHelper;

    @Autowired
    private RemoveWrinkleAsyncExecutor removeWrinkleAsyncExecutor;

    @Autowired
    private RemoveWrinkleTaskHelper removeWrinkleTaskHelper;

    @Override
    protected CreativeBatchVO buildData(RemoveWrinkle4DownCoatRequest request, MaterialModelVO modelVO) throws IOException {
        AssertUtil.assertNotBlank(request.getOriginImage(), "原始图片为空");

        CreativeBatchVO batch = new CreativeBatchVO();
        batch.setType(CreativeTypeEnum.REMOVE_WRINKLE_4_DC);
        batch.setUserId(OperationContextHolder.getMasterUserId());
        batch.setShowImage(request.getOriginImage());
        batch.setBatchCnt(2);
        batch.setOperatorId(OperationContextHolder.getOperatorUserId());
        batch.setImageProportion("NONE");
        batch.setStatus(CreativeStatusEnum.QUEUE);
        batch.setExtInfo(CommonUtil.java2JSONObject(request));
        batch.addExtInfo(KEY_REQUEST_FREQUENCIES, Arrays.asList(2, 1));
        batchFillHelper.fillOriginBatchInfo(request.getOriginImage(), null, batch);
        // imageSource 字段 和 taskId 双重验证
        if (StringUtils.equals(HISTORY, request.getImageSource())
                && ObjectUtils.isNotEmpty(batchFillHelper.getTaskByUrl(request.getOriginImage()))) {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, NO);
        } else {
            batch.addExtInfo(KEY_IS_CREATIVE_UPLOAD, YES);
        }
        removeWrinkleAsyncExecutor.storeSync(request, batch);
        return batch;
    }

    /**
     * 后置处理
     *
     * @param data    创作数据记录
     * @param request 请求
     */
    @Override
    protected void postProcess(CreativeBatchVO data, RemoveWrinkle4DownCoatRequest request) {
        List<Integer> requestFrequencies = data.getExtInfoList(KEY_REQUEST_FREQUENCIES,  Integer.class);
        AssertUtil.assertNotEmpty(requestFrequencies, "数据异常, 请求次数列表为空");

        CommonTaskVO task = removeWrinkleTaskHelper.createRemoveWrinkleTask(request.getOriginImage(), data, 0, requestFrequencies.get(0));
        AssertUtil.assertNotNull(task, "创建去皱任务失败");

        data.addExtInfo(CommonConstants.KEY_RELATED_COMMON_TASK, task.getId());
        data.addExtInfo(KEY_START_TIME, DateUtils.formatFullTime(new Date()));

        super.creativeBatchService.updateByIdSelective(data);
    }

    @Override
    public CreativeTypeEnum getCreativeType() {
        return CreativeTypeEnum.REMOVE_WRINKLE_4_DC;
    }
}
