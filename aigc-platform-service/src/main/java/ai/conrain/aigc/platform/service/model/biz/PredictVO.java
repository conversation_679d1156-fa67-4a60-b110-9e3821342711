/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2024 All Rights Reserved.
 */
package ai.conrain.aigc.platform.service.model.biz;

import ai.conrain.aigc.platform.dal.entity.UserPointDO;
import ai.conrain.aigc.platform.service.model.vo.ModelPointVO;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预估生图扣减情况
 *
 * <AUTHOR>
 * @version : PredictVO.java, v 0.1 2024/6/22 00:29 renxiao.wu Exp $
 */
@Data
public class PredictVO implements Serializable {
    private static final long serialVersionUID = -5774140145005465904L;
    /** 服装套餐内图片数量 */
    private Integer modelPoint;

    /** 赠送图片张数 */
    private BigDecimal displayGivePoint;

    /** 缪斯点数,已经除1000 */
    private BigDecimal musePoint;

    /** 当前服装套餐内图片数量 */
    private Integer currentModelPoint = 0;

    /** 当前赠送图片张数 */
    private BigDecimal currentDisplayGivePoint;

    /** 当前缪斯点数 */
    private BigDecimal currentMusePoint;

    /** 是否需要充值 */
    private boolean needTopup;

    @JsonIgnore
    private ModelPointVO modelPointVO;

    @JsonIgnore
    private UserPointDO userPointDO;
}
