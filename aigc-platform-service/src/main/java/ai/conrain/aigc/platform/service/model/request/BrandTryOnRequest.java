package ai.conrain.aigc.platform.service.model.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class BrandTryOnRequest implements CreativeRequest {
    private static final long serialVersionUID = -4440659260651418033L;

    private String key;

    @NotEmpty
    private List<ImageMaskModel> referenceImages;

    /** 服装图片 */
    @NotBlank
    private String clothImage;

    @NotBlank
    private String clothMaskUrl;

    /** 出图数量 */
    @NotNull
    private Integer imageNum;

    /** 图片来源：上传upload/历史纪录history */
    @NotBlank
    private String imageSource;

}
