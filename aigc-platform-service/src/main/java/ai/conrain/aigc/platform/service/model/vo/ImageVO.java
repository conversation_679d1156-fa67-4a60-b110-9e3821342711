package ai.conrain.aigc.platform.service.model.vo;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import java.util.Date;
import java.io.Serializable;

/**
 * ImageVO
 *
 * @version ImageService.java v 0.1 2025-07-30 02:33:24
 */
@Data
public class ImageVO implements Serializable {
	/** serialVersionUID **/
	private static final long serialVersionUID = 1L;

	/** 图像ID，自增主键 */
	private Integer id;

	/** 图像类型，用于区分服装图、风格示意图 */
	private String type;

	/** 图像原始URL地址 */
	private String url;

	/** 展示图像URL地址 */
	private String showImgUrl;

	/** 图片路径 */
	private String imagePath;

	/** 图片内容哈希 */
	private String imageHash;

	/** 图像元数据，JSON格式 */
	private JSONObject metadata;

    /** 拓展信息 */
    private JSONObject extInfo;

    /** 创建时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	/** 修改时间 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date modifyTime;

}
