package ai.conrain.aigc.platform.service.model.vo;

import ai.conrain.aigc.platform.service.enums.PointLogTypeEnum;
import ai.conrain.aigc.platform.service.enums.RoleTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserPointUsageInfoVO implements Serializable {

    private Integer pointLogId;
    private Integer userId;
    private String masterUserNickName;
    private RoleTypeEnum masterRole;
    private String masterLoginId;
    private Integer operatorUserId;
    private String operatorNickName;
    private String operatorLoginId;
    private RoleTypeEnum operatorRole;
    private Boolean masterUser;

    private PointLogTypeEnum type;

    //使用场景
    private String usageScene;
    //使用方式
    private String usageWay;

    //使用的（muse）点数
    private BigDecimal usedPoint;
    private Integer usedExperiencePoint;
    private BigDecimal usedGivePoint;
    private Integer usedModelPoint;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
