package ai.conrain.aigc.platform.service.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * UserPointVO
 *
 * @version UserPointService.java v 0.1 2024-05-15 10:58:45
 */
@Data
public class UserPointVO implements Serializable {
    /** serialVersionUID **/
    private static final long serialVersionUID = 1L;

    /** id */
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 算力点 */
    private Integer point;

    private Integer givePoint;

    /** 体验点 */
    private Integer experiencePoint;

    private BigDecimal imagePoint;

    private BigDecimal displayGivePoint;

    /** 创建时间 */
    @JsonIgnore
    private Date createTime;

    /** 修改时间 */
    @JsonIgnore
    private Date modifyTime;

    /** 用户昵称 */
    private String nickName;


}
