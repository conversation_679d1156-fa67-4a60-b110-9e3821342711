
package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.model.biz.PredictVO;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 扣费计算结果对比工具类
 */
@Slf4j
public class CalcComparator {

    /**
     * 对比新旧计算结果并记录日志
     */
    public static void compareAndLog(CalcContext context,
                                     PredictVO newResult, PredictVO oldResult,
                                     Exception newException, Exception oldException) {

        String contextInfo = String.format("type=%s, userId=%s, modelId=%s, imageNum=%s",
                context.getType(), context.getUserId(), context.getModelId(), context.getImageNum());

        // 如果两个都成功
        if (newResult != null && oldResult != null) {
            boolean isEqual = compareResults(newResult, oldResult);
            if (isEqual) {
                log.info("[扣费对比] ✅ 结果一致 - {}", contextInfo);
            } else {
                log.warn("[扣费对比] ❌ 结果不一致 - {} 新结果: {} 旧结果: {}",
                        contextInfo, JSON.toJSONString(newResult), JSON.toJSONString(oldResult));
            }
        }

        // 如果两个都失败
        if (newException != null && oldException != null) {
            boolean isSameException = newException.getClass().equals(oldException.getClass())
                    && Objects.equals(newException.getMessage(), oldException.getMessage());
            if (isSameException) {
                log.info("[扣费对比] ✅ 异常一致 - {} - {}", contextInfo, newException.getMessage());
            } else {
                log.warn("[扣费对比] ❌ 异常不一致 - {} 新异常: {} 旧异常: {}",
                        contextInfo, newException.getMessage(), oldException.getMessage());
            }
            // 重新抛出旧异常
            if (oldException instanceof RuntimeException) {
                throw (RuntimeException) oldException;
            } else {
                throw new RuntimeException("旧逻辑执行失败", oldException);
            }
        }

        // 一个成功一个失败
        if (newResult != null && oldException != null) {
            log.warn("[扣费对比] ❌ 新成功旧失败 - {} 新结果: {} 旧异常: {}",
                    contextInfo, JSON.toJSONString(newResult), oldException.getMessage());
            // 旧逻辑失败，重新抛出异常
            if (oldException instanceof RuntimeException) {
                throw (RuntimeException) oldException;
            } else {
                throw new RuntimeException("旧逻辑执行失败", oldException);
            }
        } else if (newException != null && oldResult != null) {
            log.warn("[扣费对比] ❌ 新失败旧成功 - {} 新异常: {} 旧结果: {}",
                    contextInfo, newException.getMessage(), JSON.toJSONString(oldResult));
        }

    }

    /**
     * 对比两个PredictVO是否相等
     */
    private static boolean compareResults(PredictVO newResult, PredictVO oldResult) {
        if (newResult == null && oldResult == null) {
            return true;
        }
        if (newResult == null || oldResult == null) {
            return false;
        }

        return Objects.equals(newResult.getModelPoint(), oldResult.getModelPoint())
                && compareBigDecimal(newResult.getDisplayGivePoint(), oldResult.getDisplayGivePoint())
                && compareBigDecimal(newResult.getMusePoint(), oldResult.getMusePoint())
                && compareInteger(newResult.getCurrentModelPoint(), oldResult.getCurrentModelPoint())
                && compareBigDecimal(newResult.getCurrentDisplayGivePoint(), oldResult.getCurrentDisplayGivePoint())
                && compareBigDecimal(newResult.getCurrentMusePoint(), oldResult.getCurrentMusePoint())
                && (newResult.isNeedTopup() == oldResult.isNeedTopup());
    }

    /**
     * 对比BigDecimal值，容忍精度差异
     */
    private static boolean compareBigDecimal(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) == 0;
    }

    private static boolean compareInteger(Integer a, Integer b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) == 0;
    }
}