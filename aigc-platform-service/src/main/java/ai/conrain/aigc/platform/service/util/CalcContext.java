package ai.conrain.aigc.platform.service.util;

import ai.conrain.aigc.platform.service.enums.CreativeTypeEnum;
import ai.conrain.aigc.platform.service.enums.ProportionTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.PredictVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 扣费计算上下文
 */
@Data
public class CalcContext {
    
    /** 创作类型 */
    private CreativeTypeEnum type;
    
    /** 模型id */
    private Integer modelId;
    
    /** 图片张数 */
    private Integer imageNum;
    
    /** 视频时长（秒） */
    private Integer timeSecs4Video;
    
    /** 是否上传 */
    private boolean isUpload;
    
    /** 是否需要锁记录 */
    private boolean needLock;
    
    /** 图片尺寸比例 */
    private ProportionTypeEnum proportionType;
    
    /** 用户ID */
    private Integer userId;
    
    /** 计算结果 */
    private PredictVO result;
    
    /** 剩余需要扣费的数量 */
    private BigDecimal remainingCount;
    
    public CalcContext(CreativeTypeEnum type, Integer modelId, Integer imageNum, 
                      Integer timeSecs4Video, boolean isUpload, boolean needLock, 
                      ProportionTypeEnum proportionType, Integer userId) {
        this.type = type;
        this.modelId = modelId;
        this.imageNum = imageNum;
        this.timeSecs4Video = timeSecs4Video;
        this.isUpload = isUpload;
        this.needLock = needLock;
        this.proportionType = proportionType;
        this.userId = userId;
        this.result = new PredictVO();
        this.remainingCount = BigDecimal.valueOf(imageNum);
    }
}