/**
 * conrain.ai Inc.
 * Copyright (c) 2023-2023 All Rights Reserved.
 */
package ai.conrain.aigc.platform.web.config;

import ai.conrain.aigc.platform.service.resolver.BigDecimalDeserializer;
import ai.conrain.aigc.platform.service.resolver.JsonArgResolver;
import ai.conrain.aigc.platform.web.interceptor.ImageGalleryJwtAuthInterceptor;
import ai.conrain.aigc.platform.web.interceptor.OperationContextInterceptor;
import ai.conrain.aigc.platform.web.interceptor.PermissionCheckInterceptor;
import ai.conrain.aigc.platform.web.interceptor.TraceInitInterceptor;
import ai.conrain.aigc.platform.web.interceptor.UserVisitMonitor;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.math.BigDecimal;
import java.util.List;

/**
 * web层配置
 *
 * <AUTHOR>
 * @version : WebConfigurer.java, v 0.1 2023/9/3 18:39 renxiao.wu Exp $
 */
@Configuration
public class WebConfigurer implements WebMvcConfigurer {

    //不进行登录态拦截
    private static final String[] noNeedLoginPathPatterns = {"/healthcheck", "/wx/pay/notify", "/alipay/notify", "/notify/**", "/error", "/", "/imageGallery/**"};

    /** 操作上下文拦截器 */
    @Autowired
    private OperationContextInterceptor operationContextInterceptor;
    @Autowired
    private TraceInitInterceptor traceInitInterceptor;
    @Autowired
    private PermissionCheckInterceptor permissionCheckInterceptor;

    @Autowired
    private UserVisitMonitor userVisitMonitor;

    @Autowired
    private ImageGalleryJwtAuthInterceptor imageGalleryJwtAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(traceInitInterceptor);
        registry.addInterceptor(operationContextInterceptor).excludePathPatterns(noNeedLoginPathPatterns).order(10);
        registry.addInterceptor(permissionCheckInterceptor).excludePathPatterns(noNeedLoginPathPatterns).order(20);
        registry.addInterceptor(userVisitMonitor).excludePathPatterns(noNeedLoginPathPatterns).order(20);
        registry.addInterceptor(imageGalleryJwtAuthInterceptor).addPathPatterns("/imageGallery/**").order(15);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new JsonArgResolver());
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedOriginPatterns("*").allowCredentials(true).allowedMethods("GET", "POST").maxAge(
            3600 * 24);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        
        // 配置静态资源处理器，支持嵌套的static目录
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/", "classpath:/static/static/")
                .setCachePeriod(3600)
                .resourceChain(true);
        
        // 添加根路径的静态资源处理器，用于处理直接访问的静态文件
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/", "classpath:/static/static/")
                .setCachePeriod(3600)
                .resourceChain(true);
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer customizeJackson() {
        return builder -> {
            SimpleModule module = new SimpleModule();
            module.addDeserializer(BigDecimal.class, new BigDecimalDeserializer());
            builder.modules(module);
        };
    }
}