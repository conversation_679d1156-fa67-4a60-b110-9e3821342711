package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisResult;
import ai.conrain.aigc.platform.integration.utils.IntegrationUtils;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.model.common.Result;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

@Slf4j
@Controller
public class LabelResultNotifyController {

    @Autowired
    private ImageCaptionService imageCaptionService;

    @PostMapping("/notify/label")
    @ResponseBody
    public Result onNotify(@RequestBody JSONObject jsonResponse) {
        log.info("接收到打标结果通知:{}", jsonResponse);

        ImageAnalysisResult result = new ImageAnalysisResult();
        result.setTaskId(jsonResponse.getString("taskId"));
        result.setStatus(jsonResponse.getString("status"));

        // 如果任务完成，解析分析结果
        if ("completed".equals(jsonResponse.getString("status"))) {
            ImageAnalysisCaption analysis = jsonResponse.getJSONObject("result").getObject("analysis",
                    ImageAnalysisCaption.class);
            // 处理"None"字符串，转换为null
            IntegrationUtils.processNoneToNull(analysis);
            result.setAnalysis(analysis);
            result.setRaw(jsonResponse.getJSONObject("result").toJSONString());

            imageCaptionService.onCaptionSuccess(result);

        } else if ("failed".equals(jsonResponse.getString("status"))) {
            result.setErrorMessage(jsonResponse.getString("error"));
        }

        return Result.success();
    }
}
