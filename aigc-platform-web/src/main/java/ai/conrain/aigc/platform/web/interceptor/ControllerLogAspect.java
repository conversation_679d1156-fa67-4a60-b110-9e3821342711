package ai.conrain.aigc.platform.web.interceptor;

import ai.conrain.aigc.platform.integration.utils.BeanUtils;
import ai.conrain.aigc.platform.integration.utils.BeanUtils.Callback;
import ai.conrain.aigc.platform.integration.utils.ContextSecurityUtils;
import ai.conrain.aigc.platform.service.component.PermissionService;
import ai.conrain.aigc.platform.service.component.UserOpLogService;
import ai.conrain.aigc.platform.service.model.common.BizException;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.ImageOperateVO;
import ai.conrain.aigc.platform.service.resolver.OpKey;
import ai.conrain.aigc.platform.service.util.Base64Utils;
import ai.conrain.aigc.platform.service.util.CommonUtil;
import ai.conrain.aigc.platform.service.util.OperationContextHolder;
import ai.conrain.aigc.platform.service.util.SecurityUtils;
import ai.conrain.aigc.platform.web.helper.IpUtils;
import ai.conrain.aigc.platform.web.helper.WebDigestUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Aspect
@Component
public class ControllerLogAspect {
    @Autowired
    private UserOpLogService userOpLogService;
    @Autowired
    private PermissionService permissionService;
    /** 过滤不打印日志的action */
    private static final List<String> filterActionList = new ArrayList<>();
    private static final List<String> mobileActionList = Arrays.asList("/sms/sendCaptcha", "/sms/verifyCaptcha",
        "/login/checkMobile", "/login/sms", "/login/pswd");
    private static final List<String> FIELD_LIST = Arrays.asList("mobile", "loginId");

    static {
        filterActionList.add("/location/query");
        filterActionList.add("/creative/removeBg");
        filterActionList.add("/comfyuiWorkflowTemplate/queryByPage");
        filterActionList.add("/sys/oss/download");
    }

    @Around("execution(* ai.conrain.aigc.platform.web.controller.*Controller.*(..)) ")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        log.info("打印摘要日志，进入ControllerLogAspect");
        Object object = null;
        ResultCode errorCode = null;
        JSONObject content = new JSONObject();

        try {
            object = proceedingJoinPoint.proceed();
            return object;
        } catch (BizException e) {
            errorCode = e.getCode();
            throw e;
        } catch (Throwable e) {
            //这里不catch，由上游GlobalExceptionHandler统一处理
            errorCode = ResultCode.SYS_ERROR;
            throw e;
        } finally {
            fillContent(object, proceedingJoinPoint, errorCode, content);

            //insertOpLog(content); todo:by 半泉
        }
    }

    /**
     * 填充日志内容
     *
     * @param object              结果对象
     * @param proceedingJoinPoint 处理切面点
     * @param errorCode           结果码
     * @param content             上下文内容
     */
    private void fillContent(Object object, ProceedingJoinPoint proceedingJoinPoint, ResultCode errorCode,
                             JSONObject content) {
        try {
            fillOpKey(proceedingJoinPoint);
            ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
            Signature signature = proceedingJoinPoint.getSignature();
            MethodSignature methodSignature = (MethodSignature)signature;
            Method targetMethod = methodSignature.getMethod();

            String action = null;
            String ip = "-";
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                action = request.getServletPath();
                ip = IpUtils.getIpAddr(request);
            } else {
                action = targetMethod.getDeclaringClass().getSimpleName() + "." + targetMethod.getName();
            }

            //过滤HttpServletRequest/HttpServletResponse，不可序列化

            action = permissionService.parseRequestPath(action);

            if (errorCode != null) {
                //所有的异常都在GlobalExceptionHandler中处理打印，这里不再打印
                //WebDigestUtils.digest(action, ip, false, errorCode);
            } else if (object instanceof Result) {
                Result<?> result = (Result<?>)object;
                WebDigestUtils.digest(action, ip, result.isSuccess(), result.isSuccess() ? null : result.getCode());
            } else {
                WebDigestUtils.digest(action, ip, true, null);
            }

            StringBuilder sb = new StringBuilder("[controller],").append(action).append(",");

            List<?> serializableArgs = Arrays.stream(proceedingJoinPoint.getArgs()).filter(
                a -> !(a instanceof HttpServletRequest) && !(a instanceof HttpServletResponse)
                     && !(a instanceof MultipartFile) && !(a instanceof byte[])).collect(Collectors.toList());

            try {
                String requestData = JSON.toJSONString(formatRequestArgs(action, serializableArgs));
                sb.append(requestData).append(",");
                content.put("request", requestData);
            } catch (Throwable t) {
                log.error("参数打印异常，忽略", t);
            }

            String result = "{}";
            ///location/query数据量非常大，不打印
            if (!filterActionList.contains(action)) {
                if (object == null && errorCode != null) {
                    result = JSONObject.toJSONString(errorCode.getCode());
                } else {
                    result = JSONObject.toJSONString(object);
                }
            }
            sb.append("response:").append(result);

            log.info(sb.toString());

            content.put("result", result);

        } catch (Throwable e) {
            log.error("打印controller日志异常,不影响业务", e);
        }
    }

    /**
     * 插入用户操作日志
     *
     * @param content 内容
     */
    private void insertOpLog(JSONObject content) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();

            if (null == attributes) {
                log.warn("异步插入用户操作日志失败，当前attributes为空");
                return;
            }
            HttpServletRequest request = attributes.getRequest();
            String servletPath = request.getServletPath();

            if (!permissionService.isOperateAction(servletPath)) {
                log.info("当前请求{}属于查询操作，不插入到用户操作", servletPath);
                return;
            }

        } catch (Throwable e) {
            log.error("异步插入用户操作日志异常,不影响业务", e);
        }
    }

    /**
     * 填充操作关键字
     *
     * @param proceedingJoinPoint 切面点
     */
    private void fillOpKey(ProceedingJoinPoint proceedingJoinPoint) {
        MethodSignature methodSignature = (MethodSignature)proceedingJoinPoint.getSignature();
        Parameter[] parameters = methodSignature.getMethod().getParameters();

        Object[] args = proceedingJoinPoint.getArgs();

        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            Object arg = args[i];
            if (parameter.isAnnotationPresent(OpKey.class)) {
                OperationContextHolder.getContext().setOpKey(arg);
                return;
            }

            if (StringUtils.startsWith(parameter.getParameterizedType().getTypeName(), "ai.conrain.aigc.platform")) {
                Field[] fields = arg.getClass().getDeclaredFields();
                for (Field field : fields) {
                    if (!field.isAnnotationPresent(OpKey.class)) {
                        continue;
                    }

                    Object key = null;
                    try {
                        field.setAccessible(true);
                        key = field.get(arg);
                    } catch (IllegalAccessException e) {
                        log.error("设置opKey异常，不影响业务", e);
                    }
                    OperationContextHolder.getContext().setOpKey(key);
                    return;
                }
            }
        }

    }

    /**
     * 格式化请求入参，如脱敏手机号
     *
     * @param action 请求
     * @param args   所有参数
     * @return 脱敏后的参数
     */
    private List<?> formatRequestArgs(String action, List<?> args) {
        if (CollectionUtils.isEmpty(args)) {
            return args;
        }

        List<Object> result = new ArrayList<>();
        for (Object arg : args) {
            if (arg instanceof ImageOperateVO) {
                // 对于 ImageOperateVO 类型，不记录内容
                result.add("ImageOperateVO:content-filtered");
            } else if (arg instanceof MultipartFile) {
                // 对于文件类型，只记录文件名
                MultipartFile file = (MultipartFile) arg;
                result.add("File:" + file.getOriginalFilename());
            } else if (arg instanceof byte[]) {
                // 对于字节流，只记录长度
                byte[] bytes = (byte[]) arg;
                result.add("ByteArray:length=" + bytes.length);
            } else if (arg instanceof String) {
                String strArg = (String) arg;
                if (Base64Utils.isBase64String(strArg)) {
                    // 如果是 base64 字符串，只记录长度
                    result.add("Base64:length=" + strArg.length());
                } else if (CommonUtil.isMobile(strArg)) {
                    // 如果是手机号，脱敏后加入结果列表
                    result.add(SecurityUtils.maskPhoneNumber(strArg));
                } else {
                    result.add(strArg);
                }
            } else {
                for (String field : FIELD_LIST) {
                    if (BeanUtils.hasField(arg, field)) {
                        BeanUtils.resetField(arg, field, (Callback<String>)ContextSecurityUtils::maskPhoneNumber);
                    }
                }
                // 其他类型直接加入结果列表
                result.add(arg);
            }
        }
        return result;
    }
}