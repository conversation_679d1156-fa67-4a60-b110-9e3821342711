"use strict";(self.webpackChunk=self.webpackChunk||[]).push([[60],{24779:function(Oe,M,e){e.r(M);var G=e(90228),c=e.n(G),F=e(87999),O=e.n(F),k=e(48305),o=e.n(k),$=e(46734),m=e(12564),z=e(20974),H=e(68728),J=e(22937),a=e(75271),P=e(83823),Q=e(84844),X=e(52949),Y=e(5832),w=e(87094),q=e(91364),ee=e(24573),t=e(52676),te=function(ae){var _e=ae.useSliderValue,ne=(0,a.useState)(""),g=o()(ne,2),l=g[0],h=g[1],se=(0,a.useState)(!1),D=o()(se,2),I=D[0],f=D[1],oe=(0,a.useState)(null),S=o()(oe,2),ge=S[0],re=S[1],le=(0,a.useState)(null),C=o()(le,2),r=C[0],ue=C[1],ie=(0,a.useState)(!1),R=o()(ie,2),he=R[0],T=R[1],ce=(0,a.useState)(),U=o()(ce,2),u=U[0],W=U[1],B=(0,a.useRef)(null),de=(0,a.useRef)(null),me=(0,a.useState)(!1),K=o()(me,2),L=K[0],j=K[1],fe=(0,a.useState)(!1),A=o()(fe,2),d=A[0],E=A[1],Ee=(0,a.useState)("upload"),y=o()(Ee,2),N=y[0],V=y[1],x=o()(_e,2),b=x[0],ve=x[1];(0,a.useEffect)(function(){var n=localStorage.getItem("imageUrl");n&&(h(n),V("history"));var s=localStorage.getItem("userInfo");if(s){var _=JSON.parse(s);re(_)}return Z(),function(){localStorage.removeItem("imageUrl"),localStorage.removeItem("modelId")}},[]),(0,a.useEffect)(function(){j(!!l)},[l]),(0,a.useEffect)(function(){var n=null;return u&&u.id&&u.status!=="FINISHED"&&(n=setInterval(O()(c()().mark(function s(){var _;return c()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,(0,P.DI)(u.id);case 2:_=i.sent,_&&(W(_),_.status==="FINISHED"&&(clearInterval(n),T(!1)));case 4:case"end":return i.stop()}},s)})),2e3)),function(){n&&clearInterval(n)}},[u]);function Z(){return v.apply(this,arguments)}function v(){return v=O()(c()().mark(function n(){return c()().wrap(function(_){for(;;)switch(_.prev=_.next){case 0:(0,X.m8)("REMOVE_WRINKLE",1).then(function(p){p&&ue(p)});case 1:case"end":return _.stop()}},n)})),v.apply(this,arguments)}var pe=function(){if(!l){m.ZP.error("\u8BF7\u5148\u4E0A\u4F20\u56FE\u7247");return}if(N==="upload"&&r&&r.needTopup){f(!0);return}d||(E(!0),(0,P.QX)({originImage:l}).then(function(s){s&&(m.ZP.success("\u63D0\u4EA4\u6210\u529F"),setTimeout(function(){var _;T(!0),W(s),(_=B.current)===null||_===void 0||_.refresh()},0)),E(!1)}).catch(function(s){m.ZP.error(s.message||"\u56FE\u7247\u5904\u7406\u5931\u8D25"),E(!1)}))},Me=function(s){h(s)};return(0,t.jsxs)($._z,{children:[(0,t.jsxs)(z.Z,{className:"toolkit-row-container",children:[(0,t.jsxs)("div",{className:"toolkit-work-block",children:[(0,t.jsx)(q.Z,{className:"redraw-image-upload",title:"\u5F85\u4FEE\u590D\u56FE\u7247",image:l,setImageSource:V,onImageChange:Me,historyFree:!0,uploadCost:.4}),(0,t.jsxs)("div",{className:"toolkit-number-input-container",children:[(0,t.jsx)("div",{className:"text16 font-pf color-n weight",children:"\u751F\u6210\u6570\u91CF"}),(0,t.jsx)(H.Z,{value:1,disabled:!0})]})]}),(0,t.jsx)("div",{ref:de,className:"toolkit-output-block",style:{width:"calc( 58% * ".concat(b/12,")"),maxWidth:"100%"},children:(0,t.jsx)(w.default,{sliderValue:b,changeSliderValue:ve,types:["REMOVE_WRINKLE"],ref:B,pollingTimeout:2e3})})]}),(0,t.jsx)("footer",{className:"toolkit-footer",children:(0,t.jsxs)("div",{className:"toolkit-footer-content",children:[L&&N==="upload"&&(0,t.jsx)(Q.Z,{creativeType:"REMOVE_WRINKLE",predictVO:r}),(0,t.jsxs)(J.ZP,{type:"primary",className:"create-btn",disabled:!L||d||(r==null?void 0:r.needTopup),icon:d?(0,t.jsx)(ee.Z,{style:{fontSize:16,color:"fff"}}):"",onClick:pe,children:[" ",r!=null&&r.needTopup?"\u4F59\u989D\u4E0D\u8DB3\uFF0C\u53BB\u5145\u503C":d?"\u751F\u6210\u4E2D":"\u5F00\u59CB\u53BB\u76B1"]})]})}),I&&(0,t.jsx)(Y.default,{visible:I,onClose:function(){return f(!1)},onPaySuccess:function(){f(!1),Z()}})]})};M.default=te}}]);
